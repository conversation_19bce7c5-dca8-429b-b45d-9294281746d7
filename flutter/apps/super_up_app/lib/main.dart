// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:s_translation/generated/l10n.dart';
import 'package:super_up/app/modules/splash/views/splash_view.dart';
import 'package:super_up/app/widgets/story_reply_message_widget.dart';
import 'package:super_up/app/widgets/story_like_message_widget.dart';
import 'package:super_up/v_chat_v2/v_chat_config.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:url_strategy/url_strategy.dart';
import 'package:v_chat_firebase_fcm/v_chat_firebase_fcm.dart';
import 'package:v_chat_message_page/v_chat_message_page.dart';
import 'package:v_chat_room_page/v_chat_room_page.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';
import 'package:v_platform/v_platform.dart';
import 'package:window_manager/window_manager.dart';
import 'package:v_chat_receive_share/v_chat_receive_share.dart';
import 'app/core/utils/lazy_injection.dart';
import 'app/core/widgets/main_builder.dart';
import 'firebase_options.dart';

final navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  if (VPlatforms.isDeskTop) {
    await _setDesktopWindow();
  }

  if (VPlatforms.isWeb) {
    //remove # from web url
    setPathUrlStrategy();
  }

  registerSingletons();
  VPlatformFileUtils.baseMediaUrl = SConstants.baseMediaUrl;
  if (VPlatforms.isMobile || VPlatforms.isMacOs || VPlatforms.isWeb) {
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
    }
  }
  await VAppPref.init();
  await initVChat(navigatorKey);
  vInitReceiveShareHandler();
  _initCallKit();
  runApp(
    VUtilsWrapper(
      builder: (_, local, theme) {
        return OKToast(
          position: ToastPosition.bottom,
          child: Theme(
            data: _getIosBrightness(theme) == Brightness.dark
                ? ThemeData.dark().copyWith(
                    extensions: [
                      VMessageTheme.dark().copyWith(
                        senderBubbleColor: const Color(0xff005046),
                        receiverBubbleColor: const Color(0xff363638),
                        senderTextStyle: const TextStyle(
                          color: Colors.white,
                          fontSize: 16.5,
                        ),
                        receiverTextStyle: const TextStyle(
                          color: Colors.white,
                          fontSize: 16.5,
                        ),
                        customMessageItem: (context, isMeSender, data) {
                          // Debug: Print the data structure to understand it
                          print('Custom message data: $data');
                          print('Data type: ${data.runtimeType}');
                          print('Data keys: ${data.keys.toList()}');

                          // Check if this is a story reply message
                          if (data['type'] == 'story_reply') {
                            return StoryReplyMessageWidget(
                              isMeSender: isMeSender,
                              data: data,
                            );
                          }

                          // Check if this is a story like message
                          if (data['type'] == 'story_like') {
                            return StoryLikeMessageWidget(
                              isMeSender: isMeSender,
                              data: data,
                            );
                          }

                          // Fallback for other custom message types
                          return Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: isMeSender
                                  ? Colors.blue.withValues(alpha: 0.1)
                                  : Colors.grey.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              'Custom message: ${data.toString()}',
                              style: TextStyle(
                                color: isMeSender ? Colors.white : Colors.black,
                              ),
                            ),
                          );
                        },
                      ),
                      VRoomTheme.dark().copyWith(
                          //see options here
                          ),
                    ],
                  )
                : ThemeData.light().copyWith(
                    extensions: [
                      VMessageTheme.light().copyWith(
                        senderBubbleColor: const Color(0xffE2FFD4),
                        receiverBubbleColor: const Color(0xffFFFFFF),
                        senderTextStyle: const TextStyle(
                          color: Colors.black,
                          fontSize: 16.5,
                        ),
                        receiverTextStyle: const TextStyle(
                          color: Colors.black,
                          fontSize: 16.5,
                        ),
                        customMessageItem: (context, isMeSender, data) {
                          // Debug: Print the data structure to understand it
                          print('Custom message data: $data');
                          print('Data type: ${data.runtimeType}');
                          print('Data keys: ${data.keys.toList()}');

                          // Check if this is a story reply message
                          if (data['type'] == 'story_reply') {
                            return StoryReplyMessageWidget(
                              isMeSender: isMeSender,
                              data: data,
                            );
                          }

                          // Check if this is a story like message
                          if (data['type'] == 'story_like') {
                            return StoryLikeMessageWidget(
                              isMeSender: isMeSender,
                              data: data,
                            );
                          }

                          // Fallback for other custom message types
                          return Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: isMeSender
                                  ? Colors.blue.withValues(alpha: 0.1)
                                  : Colors.grey.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              'Custom message: ${data.toString()}',
                              style: TextStyle(
                                color: Colors
                                    .black, // Always black for light theme
                              ),
                            ),
                          );
                        },
                      ),
                      VRoomTheme.light().copyWith(
                          //see options here
                          ),
                    ],
                  ),
            child: CupertinoApp(
              navigatorKey: navigatorKey,
              title: SConstants.appName,
              locale: local,
              supportedLocales: S.delegate.supportedLocales,
              localizationsDelegates: const [
                S.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              builder: (context, child) => Material(
                child: MainBuilder(
                  themeMode: theme,
                  child: child,
                ),
              ),
              home: const SplashView(),
              debugShowCheckedModeBanner: false,
              theme: CupertinoThemeData(
                brightness: _getIosBrightness(theme),
                applyThemeToAll: true,
                primaryColor: Colors.green,
                scaffoldBackgroundColor:
                    _getIosBrightness(theme) == Brightness.dark
                        ? Colors.black
                        : CupertinoColors.white,
              ),
            ),
          ),
        );
      },
    ),
  );
}

Brightness _getIosBrightness(ThemeMode themeMode) {
  if (themeMode == ThemeMode.dark) {
    return Brightness.dark;
  }
  if (themeMode == ThemeMode.light) {
    return Brightness.light;
  }
  unawaited(
    VAppPref.setStringKey(SStorageKeys.appTheme.name, ThemeMode.light.name),
  );
  return Brightness.light;
}

Future<void> _setDesktopWindow() async {
  await windowManager.ensureInitialized();
  WindowOptions windowOptions = WindowOptions(
    minimumSize: const Size(600, 1000),
    size: const Size(1500, 1000),
    backgroundColor: Colors.transparent,
    skipTaskbar: true,
    title: SConstants.appName,
    titleBarStyle: VPlatforms.isWindows ? null : TitleBarStyle.hidden,
    fullScreen: false,
  );
  await windowManager.waitUntilReadyToShow(windowOptions, () async {
    await windowManager.show();
    await windowManager.focus();
  });
  // await autoUpdater.setFeedURL(SConstants.feedUrl);
  // await autoUpdater.setScheduledCheckInterval(3600 + 12);
}

void _initCallKit() async {
  CallKeepHandler.I.configureFlutterCallKeep(true);
}
